'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'

export default function Home() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading) {
      if (isAuthenticated) {
        // Reindirizza in base al ruolo dell'utente come nel sistema React originale
        if (user?.ruolo === 'owner') {
          router.replace('/admin')
        } else if (user?.ruolo === 'user') {
          router.replace('/cantieri')
        } else if (user?.ruolo === 'cantieri_user') {
          router.replace('/cavi')
        } else {
          router.replace('/cantieri')
        }
      } else {
        // Se non autenticato, reindirizza al login
        router.replace('/login')
      }
    }
  }, [isAuthenticated, isLoading, user, router])

  // Mostra indicatore di caricamento come nel sistema React originale
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center">
      <div className="text-center">
        <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
        <p className="text-slate-600">Caricamento...</p>
      </div>
    </div>
  )
}
