{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\n\nexport default function Home() {\n  const { user, isAuthenticated, isLoading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!isLoading) {\n      if (isAuthenticated) {\n        // Reindirizza in base al ruolo dell'utente come nel sistema React originale\n        if (user?.ruolo === 'owner') {\n          router.replace('/admin')\n        } else if (user?.ruolo === 'user') {\n          router.replace('/cantieri')\n        } else if (user?.ruolo === 'cantieri_user') {\n          router.replace('/cavi')\n        } else {\n          router.replace('/cantieri')\n        }\n      } else {\n        // Se non autenticato, reindirizza al login\n        router.replace('/login')\n      }\n    }\n  }, [isAuthenticated, isLoading, user, router])\n\n  // Mostra indicatore di caricamento come nel sistema React originale\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center\">\n      <div className=\"text-center\">\n        <div className=\"w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4\"></div>\n        <p className=\"text-slate-600\">Caricamento...</p>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACnD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,CAAC,WAAW;gBACd,IAAI,iBAAiB;oBACnB,4EAA4E;oBAC5E,IAAI,MAAM,UAAU,SAAS;wBAC3B,OAAO,OAAO,CAAC;oBACjB,OAAO,IAAI,MAAM,UAAU,QAAQ;wBACjC,OAAO,OAAO,CAAC;oBACjB,OAAO,IAAI,MAAM,UAAU,iBAAiB;wBAC1C,OAAO,OAAO,CAAC;oBACjB,OAAO;wBACL,OAAO,OAAO,CAAC;oBACjB;gBACF,OAAO;oBACL,2CAA2C;oBAC3C,OAAO,OAAO,CAAC;gBACjB;YACF;QACF;yBAAG;QAAC;QAAiB;QAAW;QAAM;KAAO;IAE7C,oEAAoE;IACpE,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAE,WAAU;8BAAiB;;;;;;;;;;;;;;;;;AAItC;GAjCwB;;QACuB,kIAAA,CAAA,UAAO;QACrC,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}