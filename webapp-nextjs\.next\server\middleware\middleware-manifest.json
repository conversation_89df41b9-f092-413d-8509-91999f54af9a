{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|public).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "DmFlnj8I3P1ljkWGhQVgM+UNdCE3hT3/mJhLA6xJySg=", "__NEXT_PREVIEW_MODE_ID": "7401e11fa47f1d2eef617bb796e048e9", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "182bff4b46818cea7e2b22eeee15448908b27a39f9e8b00acdeac286946ba95a", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3c3b9e04e6ec9e37e018f061cb706a7530aa84f5f8f62ec7a102b5320f8b204e"}}}, "instrumentation": null, "functions": {}}