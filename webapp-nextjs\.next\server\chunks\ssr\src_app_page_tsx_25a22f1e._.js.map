{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\n\nexport default function Home() {\n  const { user, isAuthenticated, isLoading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!isLoading) {\n      if (isAuthenticated) {\n        // Reindirizza in base al ruolo dell'utente come nel sistema React originale\n        if (user?.ruolo === 'owner') {\n          router.replace('/admin')\n        } else if (user?.ruolo === 'user') {\n          router.replace('/cantieri')\n        } else if (user?.ruolo === 'cantieri_user') {\n          router.replace('/cavi')\n        } else {\n          router.replace('/cantieri')\n        }\n      } else {\n        // Se non autenticato, reindirizza al login\n        router.replace('/login')\n      }\n    }\n  }, [isAuthenticated, isLoading, user, router])\n\n  // Mostra indicatore di caricamento come nel sistema React originale\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center\">\n      <div className=\"text-center\">\n        <div className=\"w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4\"></div>\n        <p className=\"text-slate-600\">Caricamento...</p>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACnD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW;YACd,IAAI,iBAAiB;gBACnB,4EAA4E;gBAC5E,IAAI,MAAM,UAAU,SAAS;oBAC3B,OAAO,OAAO,CAAC;gBACjB,OAAO,IAAI,MAAM,UAAU,QAAQ;oBACjC,OAAO,OAAO,CAAC;gBACjB,OAAO,IAAI,MAAM,UAAU,iBAAiB;oBAC1C,OAAO,OAAO,CAAC;gBACjB,OAAO;oBACL,OAAO,OAAO,CAAC;gBACjB;YACF,OAAO;gBACL,2CAA2C;gBAC3C,OAAO,OAAO,CAAC;YACjB;QACF;IACF,GAAG;QAAC;QAAiB;QAAW;QAAM;KAAO;IAE7C,oEAAoE;IACpE,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAE,WAAU;8BAAiB;;;;;;;;;;;;;;;;;AAItC", "debugId": null}}]}