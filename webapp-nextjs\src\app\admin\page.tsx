'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useAuth } from '@/contexts/AuthContext'
import { api, cantieriApi, usersApi } from '@/lib/api'
import { User, Cantiere } from '@/types'
import {
  Settings,
  Users,
  Building2,
  Search,
  Plus,
  Edit,
  Trash2,
  CheckCircle,
  Clock,
  AlertCircle,
  Eye,
  Shield,
  Key,
  Loader2,
  UserPlus,
  LogIn,
  Cable,
  Database,
  RotateCcw,
  RefreshCw
} from 'lucide-react'

export default function AdminPage() {
  const [activeTab, setActiveTab] = useState('visualizza-utenti')
  const [searchTerm, setSearchTerm] = useState('')
  const [users, setUsers] = useState<User[]>([])
  const [cantieri, setCantieri] = useState<Cantiere[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' })

  const { user } = useAuth()

  // Carica dati dal backend
  useEffect(() => {
    loadData()
  }, [activeTab])

  const loadData = async () => {
    try {
      setIsLoading(true)
      setError('')

      console.log('Caricamento dati per tab:', activeTab)
      console.log('Token presente:', typeof window !== 'undefined' ? localStorage.getItem('token') : 'N/A')
      console.log('Utente corrente:', user)

      if (activeTab === 'visualizza-utenti' || activeTab === 'crea-utente' || activeTab === 'accedi-come-utente') {
        console.log('Chiamata API per ottenere utenti...')
        const usersData = await usersApi.getUsers()
        console.log('Utenti ricevuti:', usersData)
        setUsers(usersData)
      } else if (activeTab === 'cantieri') {
        const cantieriData = await cantieriApi.getCantieri()
        setCantieri(cantieriData)
      }
    } catch (error: any) {
      console.error('Errore caricamento dati:', error)
      console.error('Dettagli errore:', error.response)
      setError(error.response?.data?.detail || error.message || 'Errore durante il caricamento dei dati')
    } finally {
      setIsLoading(false)
    }
  }

  const handleEditUser = (userToEdit: User) => {
    setSelectedUser(userToEdit)
    setActiveTab('modifica-utente')
  }

  const handleToggleUserStatus = async (userId: number) => {
    try {
      await usersApi.toggleUserStatus(userId)
      loadData() // Ricarica i dati
    } catch (error: any) {
      console.error('Errore toggle status:', error)
      setError(error.response?.data?.detail || 'Errore durante la modifica dello stato utente')
    }
  }

  const handleDeleteUser = async (userId: number) => {
    if (confirm('Sei sicuro di voler eliminare questo utente?')) {
      try {
        await usersApi.deleteUser(userId)
        loadData() // Ricarica i dati
      } catch (error: any) {
        console.error('Errore eliminazione utente:', error)
        setError(error.response?.data?.detail || 'Errore durante l\'eliminazione dell\'utente')
      }
    }
  }

  const getRuoloBadge = (ruolo: string) => {
    switch (ruolo) {
      case 'owner':
        return <Badge className="bg-purple-100 text-purple-800">Owner</Badge>
      case 'user':
        return <Badge className="bg-blue-100 text-blue-800">User</Badge>
      case 'cantieri_user':
        return <Badge className="bg-green-100 text-green-800">Cantieri User</Badge>
      default:
        return <Badge variant="secondary">{ruolo}</Badge>
    }
  }

  const getStatusBadge = (abilitato: boolean, data_scadenza?: string) => {
    if (!abilitato) {
      return <Badge className="bg-red-100 text-red-800">Disabilitato</Badge>
    }
    
    if (data_scadenza) {
      const scadenza = new Date(data_scadenza)
      const oggi = new Date()
      
      if (scadenza < oggi) {
        return <Badge className="bg-red-100 text-red-800">Scaduto</Badge>
      } else if (scadenza.getTime() - oggi.getTime() < 7 * 24 * 60 * 60 * 1000) {
        return <Badge className="bg-yellow-100 text-yellow-800">In Scadenza</Badge>
      }
    }
    
    return <Badge className="bg-green-100 text-green-800">Attivo</Badge>
  }

  const filteredUsers = users.filter(u =>
    u.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    u.ragione_sociale?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    u.email?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Verifica se l'utente ha permessi di amministrazione
  if (user?.ruolo !== 'owner') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center p-6">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <Shield className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-bold text-slate-900 mb-2">Accesso Negato</h2>
            <p className="text-slate-600">Non hai i permessi necessari per accedere a questa sezione.</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
      <div className="max-w-7xl mx-auto space-y-6">

        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-slate-900 flex items-center gap-3">
              <Settings className="h-8 w-8 text-blue-600" />
              Pannello Admin
            </h1>
            <p className="text-slate-600 mt-1">Questa sezione mostra la lista di tutti gli utenti del sistema.</p>
          </div>

          {activeTab === 'crea-utente' && (
            <Button
              size="sm"
              onClick={() => setActiveTab('visualizza-utenti')}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Aggiorna
            </Button>
          )}
        </div>

        {/* Tabs - Identiche al React */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="visualizza-utenti" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Visualizza Utenti
            </TabsTrigger>
            <TabsTrigger value="crea-utente" className="flex items-center gap-2">
              <UserPlus className="h-4 w-4" />
              Crea Nuovo Utente
            </TabsTrigger>
            <TabsTrigger value="accedi-come-utente" className="flex items-center gap-2">
              <LogIn className="h-4 w-4" />
              Accedi come Utente
            </TabsTrigger>
            <TabsTrigger value="database-tipologie-cavi" className="flex items-center gap-2">
              <Cable className="h-4 w-4" />
              Database Tipologie Cavi
            </TabsTrigger>
            <TabsTrigger value="visualizza-database-raw" className="flex items-center gap-2">
              <Database className="h-4 w-4" />
              Visualizza Database Raw
            </TabsTrigger>
            <TabsTrigger value="reset-database" className="flex items-center gap-2">
              <RotateCcw className="h-4 w-4" />
              Reset Database
            </TabsTrigger>
          </TabsList>

          {/* Tab Visualizza Utenti */}
          <TabsContent value="visualizza-utenti" className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold text-slate-900">Visualizza Utenti</h2>
                <p className="text-slate-600">Questa sezione mostra la lista di tutti gli utenti del sistema.</p>
              </div>
              <Button onClick={loadData} disabled={isLoading}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Aggiorna
              </Button>
            </div>

            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <p className="text-red-600">{error}</p>
              </div>
            )}

            <Card>
              <CardHeader>
                <CardTitle>Lista Utenti</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>ID</TableHead>
                        <TableHead>Username</TableHead>
                        <TableHead>Password</TableHead>
                        <TableHead>Ruolo</TableHead>
                        <TableHead>Ragione Sociale</TableHead>
                        <TableHead>Email</TableHead>
                        <TableHead>VAT</TableHead>
                        <TableHead>Nazione</TableHead>
                        <TableHead>Referente</TableHead>
                        <TableHead>Scadenza</TableHead>
                        <TableHead>Stato</TableHead>
                        <TableHead>Azioni</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {isLoading ? (
                        <TableRow>
                          <TableCell colSpan={12} className="text-center py-8">
                            <div className="flex items-center justify-center gap-2">
                              <Loader2 className="h-4 w-4 animate-spin" />
                              Caricamento...
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : users.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={12} className="text-center py-8 text-slate-500">
                            Nessun utente trovato
                          </TableCell>
                        </TableRow>
                      ) : (
                        users.map((utente) => (
                          <TableRow key={utente.id_utente}>
                            <TableCell>{utente.id_utente}</TableCell>
                            <TableCell className="font-medium">{utente.username}</TableCell>
                            <TableCell>{utente.password_plain || '***'}</TableCell>
                            <TableCell>{getRuoloBadge(utente.ruolo)}</TableCell>
                            <TableCell>{utente.ragione_sociale || '-'}</TableCell>
                            <TableCell>{utente.email || '-'}</TableCell>
                            <TableCell>{utente.vat || '-'}</TableCell>
                            <TableCell>{utente.nazione || '-'}</TableCell>
                            <TableCell>{utente.referente_aziendale || '-'}</TableCell>
                            <TableCell>
                              {utente.data_scadenza ?
                                new Date(utente.data_scadenza).toLocaleDateString('it-IT') :
                                'N/A'
                              }
                            </TableCell>
                            <TableCell>{getStatusBadge(utente.abilitato, utente.data_scadenza)}</TableCell>
                            <TableCell>
                              <div className="flex gap-1">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleEditUser(utente)}
                                  title="Modifica"
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleToggleUserStatus(utente.id_utente)}
                                  title={utente.abilitato ? 'Disabilita' : 'Abilita'}
                                  disabled={utente.ruolo === 'owner'}
                                >
                                  {utente.abilitato ? <CheckCircle className="h-4 w-4 text-green-600" /> : <Clock className="h-4 w-4 text-red-600" />}
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDeleteUser(utente.id_utente)}
                                  title="Elimina"
                                  disabled={utente.ruolo === 'owner'}
                                >
                                  <Trash2 className="h-4 w-4 text-red-600" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Tab Crea Nuovo Utente */}
          <TabsContent value="crea-utente" className="space-y-4">
            <div>
              <h2 className="text-2xl font-bold text-slate-900">Crea Nuovo Utente Standard</h2>
              <p className="text-slate-600">Da qui puoi creare un nuovo utente standard nel sistema.</p>
            </div>
            <Card>
              <CardContent className="p-6">
                <div className="text-center py-8">
                  <UserPlus className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                  <p className="text-slate-600">Form di creazione utente - Da implementare</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Tab Accedi come Utente */}
          <TabsContent value="accedi-come-utente" className="space-y-4">
            <div>
              <h2 className="text-2xl font-bold text-slate-900">Accedi come Utente</h2>
              <p className="text-slate-600">Da qui puoi accedere al sistema impersonando un altro utente.</p>
            </div>
            <Card>
              <CardContent className="p-6">
                <div className="text-center py-8">
                  <LogIn className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                  <p className="text-slate-600">Funzionalità di impersonazione - Da implementare</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Tab Database Tipologie Cavi */}
          <TabsContent value="database-tipologie-cavi" className="space-y-4">
            <div>
              <h2 className="text-2xl font-bold text-slate-900">Database Tipologie Cavi</h2>
              <p className="text-slate-600">Gestisci il database enciclopedico delle tipologie di cavi: categorie, produttori, standard e tipologie.</p>
            </div>
            <Card>
              <CardContent className="p-6">
                <div className="text-center py-8">
                  <Cable className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                  <p className="text-slate-600">Gestione tipologie cavi - Da implementare</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Tab Visualizza Database Raw */}
          <TabsContent value="visualizza-database-raw" className="space-y-4">
            <div>
              <h2 className="text-2xl font-bold text-slate-900">Visualizzazione Database Raw</h2>
              <p className="text-slate-600">Questa sezione mostra una visualizzazione raw del database. Puoi vedere i dati delle tabelle principali.</p>
            </div>
            <Card>
              <CardContent className="p-6">
                <div className="text-center py-8">
                  <Database className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                  <p className="text-slate-600">Visualizzazione database raw - Da implementare</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Tab Reset Database */}
          <TabsContent value="reset-database" className="space-y-4">
            <div>
              <h2 className="text-2xl font-bold text-slate-900">Reset Database</h2>
              <p className="text-slate-600">Attenzione: questa operazione cancellerà tutti i dati del database.</p>
            </div>
            <Card>
              <CardContent className="p-6">
                <div className="text-center py-8">
                  <RotateCcw className="h-12 w-12 text-red-400 mx-auto mb-4" />
                  <p className="text-slate-600">Funzionalità di reset database - Da implementare</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

        </Tabs>
      </div>
    </div>
  )
}
