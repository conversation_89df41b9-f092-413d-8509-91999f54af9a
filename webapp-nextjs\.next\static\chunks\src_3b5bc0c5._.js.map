{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 188, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;KAbS;AAeT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 326, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,6LAAC,qKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;MAhCS;AAkCT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 524, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 773, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/app/cavi/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'\nimport { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { caviApi, parcoCaviApi } from '@/lib/api'\nimport { Cavo } from '@/types'\nimport {\n  Cable,\n  Search,\n  Filter,\n  Plus,\n  Edit,\n  Trash2,\n  CheckCircle,\n  Clock,\n  AlertCircle,\n  Eye,\n  Download,\n  Upload,\n  Loader2\n} from 'lucide-react'\n\nexport default function CaviPage() {\n  const [searchTerm, setSearchTerm] = useState('')\n  const [selectedStatus, setSelectedStatus] = useState('all')\n  const [cavi, setCavi] = useState<Cavo[]>([])\n  const [isLoading, setIsLoading] = useState(true)\n  const [error, setError] = useState('')\n  const [selectedCavo, setSelectedCavo] = useState<Cavo | null>(null)\n  const [showCreateDialog, setShowCreateDialog] = useState(false)\n  const [showEditDialog, setShowEditDialog] = useState(false)\n  const [showMetriDialog, setShowMetriDialog] = useState(false)\n  const [showBobinaDialog, setShowBobinaDialog] = useState(false)\n  const [bobine, setBobine] = useState<any[]>([])\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    revisione_ufficiale: '',\n    tipologia: '',\n    n_conduttori: '',\n    sezione: '',\n    ubicazione_partenza: '',\n    ubicazione_arrivo: '',\n    metri_teorici: 0,\n    responsabile_posa: '',\n    metri_posati: 0,\n    id_bobina: ''\n  })\n\n  const { user, cantiere } = useAuth()\n  const router = useRouter()\n\n  // Get cantiere ID\n  const [cantiereId, setCantiereId] = useState<number>(0)\n\n  useEffect(() => {\n    if (typeof window !== 'undefined') {\n      const storedId = cantiere?.id_cantiere || parseInt(localStorage.getItem('selectedCantiereId') || '0')\n      setCantiereId(storedId)\n    }\n  }, [cantiere])\n\n  // Carica i cavi dal backend\n  useEffect(() => {\n    if (cantiereId) {\n      loadCavi()\n      loadBobine()\n    }\n  }, [cantiereId])\n\n  const loadCavi = async () => {\n    try {\n      setIsLoading(true)\n      setError('')\n\n      if (!cantiereId) {\n        setError('Cantiere non selezionato')\n        return\n      }\n\n      const data = await caviApi.getCavi(cantiereId, {\n        search: searchTerm,\n        stato_installazione: selectedStatus === 'all' ? undefined : selectedStatus\n      })\n\n      setCavi(data)\n    } catch (error: any) {\n      console.error('Errore caricamento cavi:', error)\n      setError(error.response?.data?.detail || 'Errore durante il caricamento dei cavi')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const loadBobine = async () => {\n    try {\n      const data = await parcoCaviApi.getBobine(cantiereId)\n      setBobine(data)\n    } catch (error) {\n      console.error('Errore caricamento bobine:', error)\n    }\n  }\n\n  const handleCreateCavo = async () => {\n    try {\n      await caviApi.createCavo(cantiereId, formData)\n      setShowCreateDialog(false)\n      resetFormData()\n      loadCavi()\n    } catch (error) {\n      console.error('Errore nella creazione cavo:', error)\n      setError('Errore nella creazione del cavo')\n    }\n  }\n\n  const handleEditCavo = async () => {\n    if (!selectedCavo) return\n\n    try {\n      await caviApi.updateCavo(cantiereId, selectedCavo.id_cavo, formData)\n      setShowEditDialog(false)\n      setSelectedCavo(null)\n      resetFormData()\n      loadCavi()\n    } catch (error) {\n      console.error('Errore nella modifica cavo:', error)\n      setError('Errore nella modifica del cavo')\n    }\n  }\n\n  const handleUpdateMetri = async () => {\n    if (!selectedCavo) return\n\n    try {\n      await caviApi.updateMetriPosati(cantiereId, selectedCavo.id_cavo, formData.metri_posati)\n      setShowMetriDialog(false)\n      setSelectedCavo(null)\n      resetFormData()\n      loadCavi()\n    } catch (error) {\n      console.error('Errore nell\\'aggiornamento metri:', error)\n      setError('Errore nell\\'aggiornamento dei metri')\n    }\n  }\n\n  const handleUpdateBobina = async () => {\n    if (!selectedCavo) return\n\n    try {\n      await caviApi.updateBobina(cantiereId, selectedCavo.id_cavo, formData.id_bobina)\n      setShowBobinaDialog(false)\n      setSelectedCavo(null)\n      resetFormData()\n      loadCavi()\n    } catch (error) {\n      console.error('Errore nell\\'aggiornamento bobina:', error)\n      setError('Errore nell\\'aggiornamento della bobina')\n    }\n  }\n\n  const resetFormData = () => {\n    setFormData({\n      id_cavo: '',\n      revisione_ufficiale: '',\n      tipologia: '',\n      n_conduttori: '',\n      sezione: '',\n      ubicazione_partenza: '',\n      ubicazione_arrivo: '',\n      metri_teorici: 0,\n      responsabile_posa: '',\n      metri_posati: 0,\n      id_bobina: ''\n    })\n  }\n\n  const openEditDialog = (cavo: Cavo) => {\n    setSelectedCavo(cavo)\n    setFormData({\n      id_cavo: cavo.id_cavo || '',\n      revisione_ufficiale: cavo.revisione_ufficiale || '',\n      tipologia: cavo.tipologia || '',\n      n_conduttori: cavo.n_conduttori || '',\n      sezione: cavo.sezione || '',\n      ubicazione_partenza: cavo.ubicazione_partenza || '',\n      ubicazione_arrivo: cavo.ubicazione_arrivo || '',\n      metri_teorici: cavo.metri_teorici || 0,\n      responsabile_posa: cavo.responsabile_posa || '',\n      metri_posati: cavo.metratura_reale || 0,\n      id_bobina: cavo.id_bobina || ''\n    })\n    setShowEditDialog(true)\n  }\n\n  const openMetriDialog = (cavo: Cavo) => {\n    setSelectedCavo(cavo)\n    setFormData({\n      ...formData,\n      metri_posati: cavo.metratura_reale || 0,\n      id_bobina: cavo.id_bobina || ''\n    })\n    setShowMetriDialog(true)\n  }\n\n  const openBobinaDialog = (cavo: Cavo) => {\n    setSelectedCavo(cavo)\n    setFormData({\n      ...formData,\n      id_bobina: cavo.id_bobina || ''\n    })\n    setShowBobinaDialog(true)\n  }\n\n  // Ricarica quando cambiano i filtri\n  useEffect(() => {\n    const timeoutId = setTimeout(() => {\n      loadCavi()\n    }, 500) // Debounce di 500ms\n\n    return () => clearTimeout(timeoutId)\n  }, [searchTerm, selectedStatus])\n\n  const getStatusBadge = (stato: string | undefined) => {\n    switch (stato) {\n      case 'installato':\n        return <Badge className=\"bg-green-100 text-green-800\">Installato</Badge>\n      case 'in_corso':\n        return <Badge className=\"bg-yellow-100 text-yellow-800\">In Corso</Badge>\n      case 'non_installato':\n      case '':\n      case null:\n      case undefined:\n        return <Badge className=\"bg-gray-100 text-gray-800\">Non Installato</Badge>\n      default:\n        return <Badge variant=\"secondary\">{stato}</Badge>\n    }\n  }\n\n  const getConnectionBadge = (collegamenti: number | undefined) => {\n    switch (collegamenti) {\n      case 3:\n        return <Badge className=\"bg-green-100 text-green-800\">Collegato</Badge>\n      case 1:\n      case 2:\n        return <Badge className=\"bg-yellow-100 text-yellow-800\">Parziale</Badge>\n      case 0:\n      case null:\n      case undefined:\n        return <Badge className=\"bg-gray-100 text-gray-800\">Non Collegato</Badge>\n      default:\n        return <Badge variant=\"secondary\">Stato {collegamenti}</Badge>\n    }\n  }\n\n  const getCertificationBadge = (cavo: Cavo) => {\n    if (cavo.data_certificazione) {\n      return <Badge className=\"bg-green-100 text-green-800\">Certificato</Badge>\n    } else if (cavo.comanda_certificazione) {\n      return <Badge className=\"bg-yellow-100 text-yellow-800\">In Corso</Badge>\n    } else {\n      return <Badge className=\"bg-gray-100 text-gray-800\">Non Certificato</Badge>\n    }\n  }\n\n  const filteredCavi = cavi.filter(cavo => {\n    const matchesSearch = cavo.id_cavo?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         cavo.tipologia?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         cavo.n_conduttori?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         cavo.sezione?.toLowerCase().includes(searchTerm.toLowerCase())\n\n    const matchesStatus = selectedStatus === 'all' || cavo.stato_installazione === selectedStatus\n\n    return matchesSearch && matchesStatus\n  })\n\n  const stats = {\n    totali: cavi.length,\n    installati: cavi.filter(c => c.metratura_reale && c.metratura_reale > 0).length,\n    in_corso: cavi.filter(c => c.comanda_posa && !c.data_posa).length,\n    non_installati: cavi.filter(c => !c.metratura_reale || c.metratura_reale === 0).length\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6\">\n      <div className=\"max-w-7xl mx-auto space-y-6\">\n        \n        {/* Header */}\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-slate-900 flex items-center gap-3\">\n              <Cable className=\"h-8 w-8 text-blue-600\" />\n              Gestione Cavi\n            </h1>\n            <p className=\"text-slate-600 mt-1\">Visualizzazione e gestione completa dei cavi del cantiere</p>\n          </div>\n          \n          <div className=\"flex gap-2\">\n            <Button variant=\"outline\" size=\"sm\">\n              <Download className=\"h-4 w-4 mr-2\" />\n              Esporta\n            </Button>\n            <Button variant=\"outline\" size=\"sm\">\n              <Upload className=\"h-4 w-4 mr-2\" />\n              Importa\n            </Button>\n            <Button size=\"sm\">\n              <Plus className=\"h-4 w-4 mr-2\" />\n              Nuovo Cavo\n            </Button>\n          </div>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm text-slate-600\">Totali</p>\n                  <p className=\"text-2xl font-bold text-slate-900\">{stats.totali}</p>\n                </div>\n                <Cable className=\"h-8 w-8 text-blue-500\" />\n              </div>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm text-slate-600\">Installati</p>\n                  <p className=\"text-2xl font-bold text-green-600\">{stats.installati}</p>\n                </div>\n                <CheckCircle className=\"h-8 w-8 text-green-500\" />\n              </div>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm text-slate-600\">In Corso</p>\n                  <p className=\"text-2xl font-bold text-yellow-600\">{stats.in_corso}</p>\n                </div>\n                <Clock className=\"h-8 w-8 text-yellow-500\" />\n              </div>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm text-slate-600\">Da Installare</p>\n                  <p className=\"text-2xl font-bold text-gray-600\">{stats.non_installati}</p>\n                </div>\n                <AlertCircle className=\"h-8 w-8 text-gray-500\" />\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Filters and Search */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <Search className=\"h-5 w-5\" />\n              Ricerca e Filtri\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"flex gap-4\">\n              <div className=\"flex-1\">\n                <Input\n                  placeholder=\"Cerca per nomenclatura, tipologia o formazione...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"w-full\"\n                />\n              </div>\n              <div className=\"flex gap-2\">\n                {['all', 'installato', 'in_corso', 'non_installato'].map((status) => (\n                  <Button\n                    key={status}\n                    variant={selectedStatus === status ? 'default' : 'outline'}\n                    size=\"sm\"\n                    onClick={() => setSelectedStatus(status)}\n                  >\n                    {status === 'all' ? 'Tutti' : \n                     status === 'installato' ? 'Installati' :\n                     status === 'in_corso' ? 'In Corso' : 'Da Installare'}\n                  </Button>\n                ))}\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Cavi Table */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Elenco Cavi ({filteredCavi.length})</CardTitle>\n            <CardDescription>\n              Gestione completa dei cavi con stato installazione, collegamento e certificazione\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"rounded-md border\">\n              <Table>\n                <TableHeader>\n                  <TableRow>\n                    <TableHead>ID Cavo</TableHead>\n                    <TableHead>Tipologia</TableHead>\n                    <TableHead>Conduttori/Sezione</TableHead>\n                    <TableHead>Partenza → Arrivo</TableHead>\n                    <TableHead>Lunghezza</TableHead>\n                    <TableHead>Bobina</TableHead>\n                    <TableHead>Stato</TableHead>\n                    <TableHead>Collegamento</TableHead>\n                    <TableHead>Certificazione</TableHead>\n                    <TableHead>Azioni</TableHead>\n                  </TableRow>\n                </TableHeader>\n                <TableBody>\n                  {isLoading ? (\n                    <TableRow>\n                      <TableCell colSpan={10} className=\"text-center py-8\">\n                        <div className=\"flex items-center justify-center gap-2\">\n                          <Loader2 className=\"h-4 w-4 animate-spin\" />\n                          Caricamento cavi...\n                        </div>\n                      </TableCell>\n                    </TableRow>\n                  ) : error ? (\n                    <TableRow>\n                      <TableCell colSpan={10} className=\"text-center py-8\">\n                        <div className=\"flex items-center justify-center gap-2 text-red-600\">\n                          <AlertCircle className=\"h-4 w-4\" />\n                          {error}\n                        </div>\n                      </TableCell>\n                    </TableRow>\n                  ) : filteredCavi.length === 0 ? (\n                    <TableRow>\n                      <TableCell colSpan={10} className=\"text-center py-8 text-slate-500\">\n                        Nessun cavo trovato\n                      </TableCell>\n                    </TableRow>\n                  ) : (\n                    filteredCavi.map((cavo) => (\n                      <TableRow key={cavo.id_cavo}>\n                        <TableCell className=\"font-medium\">{cavo.id_cavo}</TableCell>\n                        <TableCell>{cavo.tipologia || '-'}</TableCell>\n                        <TableCell>\n                          <div className=\"text-sm\">\n                            <div>{cavo.n_conduttori || '-'}</div>\n                            <div className=\"text-slate-500\">{cavo.sezione || '-'}</div>\n                          </div>\n                        </TableCell>\n                        <TableCell>\n                          <div className=\"text-sm\">\n                            <div className=\"font-medium\">{cavo.ubicazione_partenza || '-'}</div>\n                            <div className=\"text-slate-500\">↓</div>\n                            <div className=\"font-medium\">{cavo.ubicazione_arrivo || '-'}</div>\n                          </div>\n                        </TableCell>\n                        <TableCell>\n                          <div className=\"text-sm\">\n                            <div>{cavo.metratura_reale || 0}/{cavo.metri_teorici || 0}m</div>\n                            <div className=\"text-slate-500\">\n                              {cavo.metri_teorici ? Math.round(((cavo.metratura_reale || 0) / cavo.metri_teorici) * 100) : 0}%\n                            </div>\n                          </div>\n                        </TableCell>\n                        <TableCell>\n                          <Badge variant={cavo.id_bobina === 'BOBINA_VUOTA' ? 'destructive' : 'secondary'}>\n                            {cavo.id_bobina || 'BOBINA_VUOTA'}\n                          </Badge>\n                        </TableCell>\n                        <TableCell>{getStatusBadge(cavo.stato_installazione)}</TableCell>\n                        <TableCell>{getConnectionBadge(cavo.collegamenti)}</TableCell>\n                        <TableCell>{getCertificationBadge(cavo)}</TableCell>\n                        <TableCell>\n                          <div className=\"flex gap-1\">\n                            <Button\n                              variant=\"ghost\"\n                              size=\"sm\"\n                              onClick={() => openMetriDialog(cavo)}\n                              disabled={cavo.stato_installazione === 'installato'}\n                            >\n                              <Eye className=\"h-4 w-4\" />\n                            </Button>\n                            <Button\n                              variant=\"ghost\"\n                              size=\"sm\"\n                              onClick={() => openEditDialog(cavo)}\n                            >\n                              <Edit className=\"h-4 w-4\" />\n                            </Button>\n                            <Button\n                              variant=\"ghost\"\n                              size=\"sm\"\n                              onClick={() => openBobinaDialog(cavo)}\n                            >\n                              <Trash2 className=\"h-4 w-4\" />\n                            </Button>\n                          </div>\n                        </TableCell>\n                      </TableRow>\n                    ))\n                  )}\n                </TableBody>\n              </Table>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Dialog Inserisci Metri */}\n        <Dialog open={showMetriDialog} onOpenChange={setShowMetriDialog}>\n          <DialogContent>\n            <DialogHeader>\n              <DialogTitle>Inserisci Metri Posati</DialogTitle>\n              <DialogDescription>\n                Cavo: {selectedCavo?.id_cavo}\n              </DialogDescription>\n            </DialogHeader>\n            <div className=\"grid gap-4 py-4\">\n              <div>\n                <Label htmlFor=\"metri_posati\">Metri Posati</Label>\n                <Input\n                  id=\"metri_posati\"\n                  type=\"number\"\n                  value={formData.metri_posati}\n                  onChange={(e) => setFormData({ ...formData, metri_posati: parseInt(e.target.value) || 0 })}\n                />\n              </div>\n              <div>\n                <Label htmlFor=\"bobina_metri\">Bobina</Label>\n                <Select value={formData.id_bobina} onValueChange={(value) => setFormData({ ...formData, id_bobina: value })}>\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Seleziona bobina\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"BOBINA_VUOTA\">BOBINA_VUOTA</SelectItem>\n                    {bobine.map((bobina) => (\n                      <SelectItem key={bobina.id_bobina} value={bobina.id_bobina}>\n                        {bobina.numero_bobina} - {bobina.tipologia} ({bobina.metri_residui}m)\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n            </div>\n            <DialogFooter>\n              <Button variant=\"outline\" onClick={() => setShowMetriDialog(false)}>\n                Annulla\n              </Button>\n              <Button onClick={handleUpdateMetri}>Salva</Button>\n            </DialogFooter>\n          </DialogContent>\n        </Dialog>\n\n        {/* Dialog Modifica Bobina */}\n        <Dialog open={showBobinaDialog} onOpenChange={setShowBobinaDialog}>\n          <DialogContent>\n            <DialogHeader>\n              <DialogTitle>Modifica Bobina</DialogTitle>\n              <DialogDescription>\n                Cavo: {selectedCavo?.id_cavo}\n              </DialogDescription>\n            </DialogHeader>\n            <div className=\"grid gap-4 py-4\">\n              <div>\n                <Label htmlFor=\"bobina_modifica\">Nuova Bobina</Label>\n                <Select value={formData.id_bobina} onValueChange={(value) => setFormData({ ...formData, id_bobina: value })}>\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Seleziona bobina\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"BOBINA_VUOTA\">BOBINA_VUOTA</SelectItem>\n                    {bobine.map((bobina) => (\n                      <SelectItem key={bobina.id_bobina} value={bobina.id_bobina}>\n                        {bobina.numero_bobina} - {bobina.tipologia} ({bobina.metri_residui}m)\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n            </div>\n            <DialogFooter>\n              <Button variant=\"outline\" onClick={() => setShowBobinaDialog(false)}>\n                Annulla\n              </Button>\n              <Button onClick={handleUpdateBobina}>Salva</Button>\n            </DialogFooter>\n          </DialogContent>\n        </Dialog>\n\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAfA;;;;;;;;;;;;;;AA+Be,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC9C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,SAAS;QACT,qBAAqB;QACrB,WAAW;QACX,cAAc;QACd,SAAS;QACT,qBAAqB;QACrB,mBAAmB;QACnB,eAAe;QACf,mBAAmB;QACnB,cAAc;QACd,WAAW;IACb;IAEA,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACjC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,kBAAkB;IAClB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAErD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,wCAAmC;gBACjC,MAAM,WAAW,UAAU,eAAe,SAAS,aAAa,OAAO,CAAC,yBAAyB;gBACjG,cAAc;YAChB;QACF;6BAAG;QAAC;KAAS;IAEb,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,YAAY;gBACd;gBACA;YACF;QACF;6BAAG;QAAC;KAAW;IAEf,MAAM,WAAW;QACf,IAAI;YACF,aAAa;YACb,SAAS;YAET,IAAI,CAAC,YAAY;gBACf,SAAS;gBACT;YACF;YAEA,MAAM,OAAO,MAAM,oHAAA,CAAA,UAAO,CAAC,OAAO,CAAC,YAAY;gBAC7C,QAAQ;gBACR,qBAAqB,mBAAmB,QAAQ,YAAY;YAC9D;YAEA,QAAQ;QACV,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS,MAAM,QAAQ,EAAE,MAAM,UAAU;QAC3C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,OAAO,MAAM,oHAAA,CAAA,eAAY,CAAC,SAAS,CAAC;YAC1C,UAAU;QACZ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,oHAAA,CAAA,UAAO,CAAC,UAAU,CAAC,YAAY;YACrC,oBAAoB;YACpB;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,SAAS;QACX;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,cAAc;QAEnB,IAAI;YACF,MAAM,oHAAA,CAAA,UAAO,CAAC,UAAU,CAAC,YAAY,aAAa,OAAO,EAAE;YAC3D,kBAAkB;YAClB,gBAAgB;YAChB;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,SAAS;QACX;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,cAAc;QAEnB,IAAI;YACF,MAAM,oHAAA,CAAA,UAAO,CAAC,iBAAiB,CAAC,YAAY,aAAa,OAAO,EAAE,SAAS,YAAY;YACvF,mBAAmB;YACnB,gBAAgB;YAChB;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,SAAS;QACX;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,cAAc;QAEnB,IAAI;YACF,MAAM,oHAAA,CAAA,UAAO,CAAC,YAAY,CAAC,YAAY,aAAa,OAAO,EAAE,SAAS,SAAS;YAC/E,oBAAoB;YACpB,gBAAgB;YAChB;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,SAAS;QACX;IACF;IAEA,MAAM,gBAAgB;QACpB,YAAY;YACV,SAAS;YACT,qBAAqB;YACrB,WAAW;YACX,cAAc;YACd,SAAS;YACT,qBAAqB;YACrB,mBAAmB;YACnB,eAAe;YACf,mBAAmB;YACnB,cAAc;YACd,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,gBAAgB;QAChB,YAAY;YACV,SAAS,KAAK,OAAO,IAAI;YACzB,qBAAqB,KAAK,mBAAmB,IAAI;YACjD,WAAW,KAAK,SAAS,IAAI;YAC7B,cAAc,KAAK,YAAY,IAAI;YACnC,SAAS,KAAK,OAAO,IAAI;YACzB,qBAAqB,KAAK,mBAAmB,IAAI;YACjD,mBAAmB,KAAK,iBAAiB,IAAI;YAC7C,eAAe,KAAK,aAAa,IAAI;YACrC,mBAAmB,KAAK,iBAAiB,IAAI;YAC7C,cAAc,KAAK,eAAe,IAAI;YACtC,WAAW,KAAK,SAAS,IAAI;QAC/B;QACA,kBAAkB;IACpB;IAEA,MAAM,kBAAkB,CAAC;QACvB,gBAAgB;QAChB,YAAY;YACV,GAAG,QAAQ;YACX,cAAc,KAAK,eAAe,IAAI;YACtC,WAAW,KAAK,SAAS,IAAI;QAC/B;QACA,mBAAmB;IACrB;IAEA,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;QAChB,YAAY;YACV,GAAG,QAAQ;YACX,WAAW,KAAK,SAAS,IAAI;QAC/B;QACA,oBAAoB;IACtB;IAEA,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,MAAM,YAAY;gDAAW;oBAC3B;gBACF;+CAAG,KAAK,oBAAoB;;YAE5B;sCAAO,IAAM,aAAa;;QAC5B;6BAAG;QAAC;QAAY;KAAe;IAE/B,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA8B;;;;;;YACxD,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAgC;;;;;;YAC1D,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA4B;;;;;;YACtD;gBACE,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAa;;;;;;QACvC;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA8B;;;;;;YACxD,KAAK;YACL,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAgC;;;;;;YAC1D,KAAK;YACL,KAAK;YACL,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA4B;;;;;;YACtD;gBACE,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;;wBAAY;wBAAO;;;;;;;QAC7C;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,IAAI,KAAK,mBAAmB,EAAE;YAC5B,qBAAO,6LAAC,oIAAA,CAAA,QAAK;gBAAC,WAAU;0BAA8B;;;;;;QACxD,OAAO,IAAI,KAAK,sBAAsB,EAAE;YACtC,qBAAO,6LAAC,oIAAA,CAAA,QAAK;gBAAC,WAAU;0BAAgC;;;;;;QAC1D,OAAO;YACL,qBAAO,6LAAC,oIAAA,CAAA,QAAK;gBAAC,WAAU;0BAA4B;;;;;;QACtD;IACF;IAEA,MAAM,eAAe,KAAK,MAAM,CAAC,CAAA;QAC/B,MAAM,gBAAgB,KAAK,OAAO,EAAE,cAAc,SAAS,WAAW,WAAW,OAC5D,KAAK,SAAS,EAAE,cAAc,SAAS,WAAW,WAAW,OAC7D,KAAK,YAAY,EAAE,cAAc,SAAS,WAAW,WAAW,OAChE,KAAK,OAAO,EAAE,cAAc,SAAS,WAAW,WAAW;QAEhF,MAAM,gBAAgB,mBAAmB,SAAS,KAAK,mBAAmB,KAAK;QAE/E,OAAO,iBAAiB;IAC1B;IAEA,MAAM,QAAQ;QACZ,QAAQ,KAAK,MAAM;QACnB,YAAY,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,eAAe,IAAI,EAAE,eAAe,GAAG,GAAG,MAAM;QAC/E,UAAU,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,IAAI,CAAC,EAAE,SAAS,EAAE,MAAM;QACjE,gBAAgB,KAAK,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,eAAe,IAAI,EAAE,eAAe,KAAK,GAAG,MAAM;IACxF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAGb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAA0B;;;;;;;8CAG7C,6LAAC;oCAAE,WAAU;8CAAsB;;;;;;;;;;;;sCAGrC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;;sDAC7B,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGvC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;;sDAC7B,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGrC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,MAAK;;sDACX,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;8BAOvC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAyB;;;;;;8DACtC,6LAAC;oDAAE,WAAU;8DAAqC,MAAM,MAAM;;;;;;;;;;;;sDAEhE,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAKvB,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAyB;;;;;;8DACtC,6LAAC;oDAAE,WAAU;8DAAqC,MAAM,UAAU;;;;;;;;;;;;sDAEpE,6LAAC,8NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAK7B,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAyB;;;;;;8DACtC,6LAAC;oDAAE,WAAU;8DAAsC,MAAM,QAAQ;;;;;;;;;;;;sDAEnE,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAKvB,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAyB;;;;;;8DACtC,6LAAC;oDAAE,WAAU;8DAAoC,MAAM,cAAc;;;;;;;;;;;;sDAEvE,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO/B,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;sCAIlC,6LAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;;;;;;;;;;kDAGd,6LAAC;wCAAI,WAAU;kDACZ;4CAAC;4CAAO;4CAAc;4CAAY;yCAAiB,CAAC,GAAG,CAAC,CAAC,uBACxD,6LAAC,qIAAA,CAAA,SAAM;gDAEL,SAAS,mBAAmB,SAAS,YAAY;gDACjD,MAAK;gDACL,SAAS,IAAM,kBAAkB;0DAEhC,WAAW,QAAQ,UACnB,WAAW,eAAe,eAC1B,WAAW,aAAa,aAAa;+CAPjC;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAgBjB,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;;8CACT,6LAAC,mIAAA,CAAA,YAAS;;wCAAC;wCAAc,aAAa,MAAM;wCAAC;;;;;;;8CAC7C,6LAAC,mIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAInB,6LAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;;sDACJ,6LAAC,oIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;;kEACP,6LAAC,oIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,oIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,oIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,oIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,oIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,oIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,oIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,oIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,oIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,oIAAA,CAAA,YAAS;kEAAC;;;;;;;;;;;;;;;;;sDAGf,6LAAC,oIAAA,CAAA,YAAS;sDACP,0BACC,6LAAC,oIAAA,CAAA,WAAQ;0DACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;oDAAC,SAAS;oDAAI,WAAU;8DAChC,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,oNAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;4DAAyB;;;;;;;;;;;;;;;;uDAKhD,sBACF,6LAAC,oIAAA,CAAA,WAAQ;0DACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;oDAAC,SAAS;oDAAI,WAAU;8DAChC,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DACtB;;;;;;;;;;;;;;;;uDAIL,aAAa,MAAM,KAAK,kBAC1B,6LAAC,oIAAA,CAAA,WAAQ;0DACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;oDAAC,SAAS;oDAAI,WAAU;8DAAkC;;;;;;;;;;uDAKtE,aAAa,GAAG,CAAC,CAAC,qBAChB,6LAAC,oIAAA,CAAA,WAAQ;;sEACP,6LAAC,oIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAe,KAAK,OAAO;;;;;;sEAChD,6LAAC,oIAAA,CAAA,YAAS;sEAAE,KAAK,SAAS,IAAI;;;;;;sEAC9B,6LAAC,oIAAA,CAAA,YAAS;sEACR,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAK,KAAK,YAAY,IAAI;;;;;;kFAC3B,6LAAC;wEAAI,WAAU;kFAAkB,KAAK,OAAO,IAAI;;;;;;;;;;;;;;;;;sEAGrD,6LAAC,oIAAA,CAAA,YAAS;sEACR,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFAAe,KAAK,mBAAmB,IAAI;;;;;;kFAC1D,6LAAC;wEAAI,WAAU;kFAAiB;;;;;;kFAChC,6LAAC;wEAAI,WAAU;kFAAe,KAAK,iBAAiB,IAAI;;;;;;;;;;;;;;;;;sEAG5D,6LAAC,oIAAA,CAAA,YAAS;sEACR,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;;4EAAK,KAAK,eAAe,IAAI;4EAAE;4EAAE,KAAK,aAAa,IAAI;4EAAE;;;;;;;kFAC1D,6LAAC;wEAAI,WAAU;;4EACZ,KAAK,aAAa,GAAG,KAAK,KAAK,CAAC,AAAC,CAAC,KAAK,eAAe,IAAI,CAAC,IAAI,KAAK,aAAa,GAAI,OAAO;4EAAE;;;;;;;;;;;;;;;;;;sEAIrG,6LAAC,oIAAA,CAAA,YAAS;sEACR,cAAA,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAS,KAAK,SAAS,KAAK,iBAAiB,gBAAgB;0EACjE,KAAK,SAAS,IAAI;;;;;;;;;;;sEAGvB,6LAAC,oIAAA,CAAA,YAAS;sEAAE,eAAe,KAAK,mBAAmB;;;;;;sEACnD,6LAAC,oIAAA,CAAA,YAAS;sEAAE,mBAAmB,KAAK,YAAY;;;;;;sEAChD,6LAAC,oIAAA,CAAA,YAAS;sEAAE,sBAAsB;;;;;;sEAClC,6LAAC,oIAAA,CAAA,YAAS;sEACR,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,qIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,SAAS,IAAM,gBAAgB;wEAC/B,UAAU,KAAK,mBAAmB,KAAK;kFAEvC,cAAA,6LAAC,mMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;;;;;;kFAEjB,6LAAC,qIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,SAAS,IAAM,eAAe;kFAE9B,cAAA,6LAAC,8MAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;;;;;;kFAElB,6LAAC,qIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,SAAS,IAAM,iBAAiB;kFAEhC,cAAA,6LAAC,6MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;mDAtDX,KAAK,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAoEzC,6LAAC,qIAAA,CAAA,SAAM;oBAAC,MAAM;oBAAiB,cAAc;8BAC3C,cAAA,6LAAC,qIAAA,CAAA,gBAAa;;0CACZ,6LAAC,qIAAA,CAAA,eAAY;;kDACX,6LAAC,qIAAA,CAAA,cAAW;kDAAC;;;;;;kDACb,6LAAC,qIAAA,CAAA,oBAAiB;;4CAAC;4CACV,cAAc;;;;;;;;;;;;;0CAGzB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAe;;;;;;0DAC9B,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,YAAY;gDAC5B,UAAU,CAAC,IAAM,YAAY;wDAAE,GAAG,QAAQ;wDAAE,cAAc,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;oDAAE;;;;;;;;;;;;kDAG5F,6LAAC;;0DACC,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAe;;;;;;0DAC9B,6LAAC,qIAAA,CAAA,SAAM;gDAAC,OAAO,SAAS,SAAS;gDAAE,eAAe,CAAC,QAAU,YAAY;wDAAE,GAAG,QAAQ;wDAAE,WAAW;oDAAM;;kEACvG,6LAAC,qIAAA,CAAA,gBAAa;kEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,6LAAC,qIAAA,CAAA,gBAAa;;0EACZ,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAe;;;;;;4DAChC,OAAO,GAAG,CAAC,CAAC,uBACX,6LAAC,qIAAA,CAAA,aAAU;oEAAwB,OAAO,OAAO,SAAS;;wEACvD,OAAO,aAAa;wEAAC;wEAAI,OAAO,SAAS;wEAAC;wEAAG,OAAO,aAAa;wEAAC;;mEADpD,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQ3C,6LAAC,qIAAA,CAAA,eAAY;;kDACX,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,SAAS,IAAM,mBAAmB;kDAAQ;;;;;;kDAGpE,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAS;kDAAmB;;;;;;;;;;;;;;;;;;;;;;;8BAM1C,6LAAC,qIAAA,CAAA,SAAM;oBAAC,MAAM;oBAAkB,cAAc;8BAC5C,cAAA,6LAAC,qIAAA,CAAA,gBAAa;;0CACZ,6LAAC,qIAAA,CAAA,eAAY;;kDACX,6LAAC,qIAAA,CAAA,cAAW;kDAAC;;;;;;kDACb,6LAAC,qIAAA,CAAA,oBAAiB;;4CAAC;4CACV,cAAc;;;;;;;;;;;;;0CAGzB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;;sDACC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAkB;;;;;;sDACjC,6LAAC,qIAAA,CAAA,SAAM;4CAAC,OAAO,SAAS,SAAS;4CAAE,eAAe,CAAC,QAAU,YAAY;oDAAE,GAAG,QAAQ;oDAAE,WAAW;gDAAM;;8DACvG,6LAAC,qIAAA,CAAA,gBAAa;8DACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,6LAAC,qIAAA,CAAA,gBAAa;;sEACZ,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAe;;;;;;wDAChC,OAAO,GAAG,CAAC,CAAC,uBACX,6LAAC,qIAAA,CAAA,aAAU;gEAAwB,OAAO,OAAO,SAAS;;oEACvD,OAAO,aAAa;oEAAC;oEAAI,OAAO,SAAS;oEAAC;oEAAG,OAAO,aAAa;oEAAC;;+DADpD,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQ3C,6LAAC,qIAAA,CAAA,eAAY;;kDACX,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,SAAS,IAAM,oBAAoB;kDAAQ;;;;;;kDAGrE,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAS;kDAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnD;GAnkBwB;;QA0BK,kIAAA,CAAA,UAAO;QACnB,qIAAA,CAAA,YAAS;;;KA3BF", "debugId": null}}]}